'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useLanguage } from '@/hooks/useLanguage';
import { useConfirmDialog } from '@/contexts/DialogContext';
import { DashboardData } from '@/types';
import { ZODIAC_INFO } from '@/utils/zodiac';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import ZodiacCard from '@/components/ZodiacCard';
import TranslatedText from '@/components/TranslatedText';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import PWAInstaller from '@/components/PWAInstaller';
import MobileNavigation from '@/components/MobileNavigation';
import BirthChart from '@/components/BirthChart';
import { LogOut, Star, Calendar, Clock, Palette, Hash, BookOpen, Heart, Briefcase, Activity, Gem, Users, Smile } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';
import { appLogger, LogCategory, LogLevel, logUserAction, logAPICall, logAPIResponse, logError } from '@/utils/app-logger';

export default function Dashboard() {
  const { user, isAuthenticated, loading: authLoading, getSessionTimeRemaining, isSessionExpired, logout } = useAuth();
  const { language, setLanguage } = useLanguage();
  const { t } = useUITranslation();
  const { confirmLogout } = useConfirmDialog();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'horoscope' | 'guide'>('horoscope');
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number>(0);
  const router = useRouter();

  // Update session time remaining every minute
  useEffect(() => {
    if (!isAuthenticated) return;

    const updateSessionTime = () => {
      const remaining = getSessionTimeRemaining();
      setSessionTimeRemaining(remaining);

      // If session expired, redirect to home
      if (remaining <= 0 || isSessionExpired()) {
        console.log('Session expired, redirecting to home');
        router.push('/');
        return;
      }
    };

    // Update immediately
    updateSessionTime();

    // Update every minute
    const interval = setInterval(updateSessionTime, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, getSessionTimeRemaining, isSessionExpired, router]);

  // Track if we've already set the initial language preference
  const [hasSetInitialLanguage, setHasSetInitialLanguage] = useState(false);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/');
      return;
    }

    // Only set language preference once when user first loads, not on every user change
    if (user && user.languagePreference && !hasSetInitialLanguage) {
      console.log('🌐 Setting initial user language preference:', user.languagePreference);
      setLanguage(user.languagePreference);
      setHasSetInitialLanguage(true);
    }
  }, [user, isAuthenticated, authLoading, router, setLanguage, hasSetInitialLanguage]);

  // Separate effect to fetch data when user changes (but not language)
  useEffect(() => {
    if (user && !authLoading && isAuthenticated) {
      fetchDashboardData();
    }
  }, [user, authLoading, isAuthenticated]);

  // Effect to refetch data when language changes (for pre-translated content)
  useEffect(() => {
    if (user && !authLoading && isAuthenticated && dashboardData) {
      console.log('🌐 Language changed, refetching dashboard data for translations');
      fetchDashboardData();
    }
  }, [language]);

  const fetchDashboardData = async () => {
    if (!user) {
      appLogger.warn(LogCategory.API, 'Dashboard fetch skipped - no user', { userId: user?.id });
      return;
    }

    try {
      setLoading(true);
      logAPICall('/api/dashboard', 'GET', { userId: user.id, language }, user.id);

      const response = await fetch(`/api/dashboard?userId=${user.id}&language=${language}`);
      const data = await response.json();

      if (data.success) {
        logAPIResponse('/api/dashboard', true, {
          hasData: !!data.data,
          hasDailyReading: !!data.data?.dailyReading,
          hasBirthChart: !!data.data?.birthChart,
          hasTranslations: !!data.data?.dailyReadingTranslations
        }, user.id);

        appLogger.info(LogCategory.DAILY_GUIDE, 'Dashboard data loaded successfully', {
          dailyReadingDate: data.data?.dailyReading?.date,
          translationsAvailable: {
            en: !!data.data?.dailyReadingTranslations?.en,
            si: !!data.data?.dailyReadingTranslations?.si
          }
        }, user.id);

        setDashboardData(data.data);
      } else {
        logAPIResponse('/api/dashboard', false, { error: data.error }, user.id);
        setError(data.error || 'Failed to load dashboard data');
      }
    } catch (err) {
      logError(err as Error, 'Dashboard data fetch', user.id);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {
    logUserAction('Language Change Requested', { from: language, to: newLanguage }, user?.id);

    // Update language context immediately - this will trigger:
    // 1. TranslatedText components to update
    // 2. The language useEffect to refetch dashboard data with new translations
    setLanguage(newLanguage);
    appLogger.info(LogCategory.TRANSLATION, 'Language context updated', { newLanguage }, user?.id);

    // Update user preference in backend asynchronously (don't wait for it)
    if (user) {
      logAPICall('/api/dashboard', 'POST', { userId: user.id, language: newLanguage }, user.id);

      fetch('/api/dashboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id, language: newLanguage })
      }).then(response => {
        if (response.ok) {
          logAPIResponse('/api/dashboard', true, { action: 'language_preference_update' }, user.id);
        } else {
          logAPIResponse('/api/dashboard', false, { action: 'language_preference_update' }, user.id);
        }
      }).catch(error => {
        logError(error as Error, 'Language preference update', user.id);
      });
    }
  };

  // Helper function to get daily reading translations safely
  const getDailyReadingTranslations = (field: string) => {
    console.log('🔍 getDailyReadingTranslations called for field:', field);
    appLogger.debug(LogCategory.TRANSLATION, 'getDailyReadingTranslations called', {
      field,
      hasDashboardData: !!dashboardData,
      hasTranslations: !!dashboardData?.dailyReadingTranslations,
      translationsStructure: dashboardData?.dailyReadingTranslations ? {
        hasEn: !!dashboardData.dailyReadingTranslations.en,
        hasSi: !!dashboardData.dailyReadingTranslations.si,
        enKeys: dashboardData.dailyReadingTranslations.en ? Object.keys(dashboardData.dailyReadingTranslations.en) : [],
        siKeys: dashboardData.dailyReadingTranslations.si ? Object.keys(dashboardData.dailyReadingTranslations.si) : []
      } : null
    }, user?.id);

    if (!dashboardData?.dailyReadingTranslations) {
      console.log('❌ No dailyReadingTranslations available for field:', field);
      appLogger.warn(LogCategory.TRANSLATION, 'No dailyReadingTranslations available', { field }, user?.id);
      return undefined;
    }

    const translations: { en?: string; si?: string } = {};

    // Always include English (fallback to original text)
    if (dashboardData.dailyReadingTranslations.en?.[field]) {
      translations.en = dashboardData.dailyReadingTranslations.en[field];
      console.log('✅ Found English translation for field:', field);
      appLogger.debug(LogCategory.TRANSLATION, 'Found English translation', { field, value: translations.en?.substring(0, 50) + '...' }, user?.id);
    } else if (dashboardData.dailyReading?.[field]) {
      translations.en = dashboardData.dailyReading[field];
      console.log('⚠️ Using fallback English from dailyReading for field:', field);
      appLogger.debug(LogCategory.TRANSLATION, 'Using fallback English from dailyReading', { field, value: translations.en?.substring(0, 50) + '...' }, user?.id);
    }

    // Include Sinhala if available
    if (dashboardData.dailyReadingTranslations.si?.[field]) {
      translations.si = dashboardData.dailyReadingTranslations.si[field];
      console.log('✅ Found Sinhala translation for field:', field);
      appLogger.debug(LogCategory.TRANSLATION, 'Found Sinhala translation', { field, value: translations.si?.substring(0, 50) + '...' }, user?.id);
    } else {
      console.log('❌ No Sinhala translation found for field:', field);
      appLogger.warn(LogCategory.TRANSLATION, 'No Sinhala translation found', { field }, user?.id);
    }

    const result = Object.keys(translations).length > 0 ? translations : undefined;
    console.log('📊 getDailyReadingTranslations result for field:', field, 'result:', result);
    appLogger.debug(LogCategory.TRANSLATION, 'getDailyReadingTranslations result', {
      field,
      hasResult: !!result,
      resultKeys: result ? Object.keys(result) : [],
      resultStructure: result
    }, user?.id);

    return result;
  };

  const handleLogout = async () => {
    const confirmed = await confirmLogout();
    if (confirmed) {
      logout();
      router.push('/');
    }
  };

  const handleCalculateBirthChart = async () => {
    if (!user) return;

    try {
      console.log('🔮 Calculating birth chart for user:', user.id);

      const response = await fetch('/api/birth-chart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId: user.id })
      });

      const data = await response.json();

      if (data.success) {
        console.log('✅ Birth chart calculated successfully');
        // Refresh dashboard data to show the new birth chart
        fetchDashboardData();
      } else {
        console.error('❌ Failed to calculate birth chart:', data.error);
        alert(`Failed to calculate birth chart: ${data.error}`);
      }
    } catch (error) {
      console.error('❌ Error calculating birth chart:', error);
      alert('Error calculating birth chart. Please try again.');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <LoadingSpinner message={t('loading_cosmic_insights')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title={t('error_loading_dashboard')}
          message={error}
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  if (!dashboardData || !user) {
    return null;
  }

  const zodiacInfo = ZODIAC_INFO[user.zodiacSign];

  // Fallback if zodiac info is not found
  if (!zodiacInfo) {
    console.error('Zodiac info not found for sign:', user.zodiacSign);
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title={t('configuration_error')}
          message="Unable to load zodiac information. Please contact support."
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Session Warning */}
      {sessionTimeRemaining <= 5 && sessionTimeRemaining > 0 && (
        <div className="bg-red-600/90 backdrop-blur-sm text-white px-4 py-2 text-center text-sm font-medium z-[200] relative">
          ⚠️ Your session will expire in {sessionTimeRemaining} minute{sessionTimeRemaining !== 1 ? 's' : ''}. Please scan your QR code again to continue.
        </div>
      )}

      {/* Mobile Header - Redesigned */}
      <header className="md:hidden bg-black/20 backdrop-blur-sm border-b border-white/10 sticky top-0 z-[90]">
        <div className="px-4 py-3">
          {/* Top Row: User Info + Menu */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="text-2xl flex-shrink-0">{zodiacInfo.symbol}</div>
              <div className="min-w-0 flex-1">
                <h1 className="text-base font-bold text-white truncate">{t('welcome')}, {user.name}</h1>
                <p className="text-gray-300 text-xs truncate">{zodiacInfo.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <LanguageSwitcher onLanguageChange={handleLanguageChange} />
              <MobileNavigation
                activeTab={activeTab}
                onTabChange={(tab) => setActiveTab(tab as 'horoscope' | 'guide')}
                onLogout={handleLogout}
              />
            </div>
          </div>

          {/* Bottom Row: Tab Navigation */}
          <div className="flex items-center justify-center">
            <div className="flex bg-white/10 rounded-lg p-1 w-full max-w-sm">
              <button
                onClick={() => setActiveTab('horoscope')}
                className={`flex items-center justify-center space-x-2 px-3 py-2 rounded-md transition-colors flex-1 ${
                  activeTab === 'horoscope'
                    ? 'bg-white/20 text-white shadow-sm'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                <BookOpen size={16} />
                <span className="text-sm font-medium">{t('horoscope')}</span>
              </button>
              <button
                onClick={() => setActiveTab('guide')}
                className={`flex items-center justify-center space-x-2 px-3 py-2 rounded-md transition-colors flex-1 ${
                  activeTab === 'guide'
                    ? 'bg-white/20 text-white shadow-sm'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                <Clock size={16} />
                <span className="text-sm font-medium">{t('daily_guide')}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Desktop Header */}
      <header className="hidden md:block bg-black/20 backdrop-blur-sm border-b border-white/10 relative z-[90]">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-3xl">{zodiacInfo.symbol}</div>
              <div>
                <h1 className="text-xl font-bold text-white">{t('welcome')}, {user.name}</h1>
                <p className="text-gray-300 text-sm">{zodiacInfo.name} • {zodiacInfo.dates}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Session Timer */}
              <div className="flex items-center space-x-2 text-sm">
                <Clock size={16} className="text-yellow-400" />
                <span className={`font-medium ${sessionTimeRemaining <= 5 ? 'text-red-400' : sessionTimeRemaining <= 10 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {sessionTimeRemaining}m
                </span>
              </div>

              <div className="flex-shrink-0">
                <LanguageSwitcher onLanguageChange={handleLanguageChange} />
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 text-gray-300 hover:text-red-400 transition-colors"
                title={t('logout')}
              >
                <LogOut size={20} />
                <span className="text-sm">{t('logout')}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Desktop Navigation Tabs */}
      <nav className="hidden md:block bg-black/10 backdrop-blur-sm border-b border-white/10 relative z-[80]">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'horoscope', label: t('horoscope'), icon: BookOpen },
              { id: 'guide', label: t('daily_guide'), icon: Clock }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-purple-400 text-white'
                    : 'border-transparent text-gray-300 hover:text-white'
                }`}
              >
                <Icon size={16} />
                <span>{label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Mobile Content */}
      <main className="md:hidden px-3 py-4 pb-6 relative z-[10]">
        {activeTab === 'horoscope' && (
          <div className="space-y-4">
            {/* Birth Chart (Handahana) - New Calculated System */}
            {dashboardData.birthChart ? (
              <BirthChart birthChart={dashboardData.birthChart} />
            ) : (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
                <Star className="mx-auto mb-4 text-yellow-400" size={48} />
                <h3 className="text-lg font-semibold text-white mb-2">
                  <TranslatedText text="Your Birth Chart (Handahana) Awaits" />
                </h3>
                <p className="text-gray-300 text-sm mb-4">
                  <TranslatedText text="Get your personalized Vedic astrology birth chart with detailed interpretations based on your exact birth time and location." />
                </p>
                {user?.birthTime && user?.birthPlace ? (
                  <button
                    onClick={handleCalculateBirthChart}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg"
                  >
                    <TranslatedText text="Calculate My Birth Chart" />
                  </button>
                ) : (
                  <div className="text-center">
                    <p className="text-yellow-300 text-sm mb-3">
                      <TranslatedText text="Birth time and place are required for accurate calculations." />
                    </p>
                    <p className="text-gray-400 text-xs">
                      <TranslatedText text="Please contact admin to update your birth details." />
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Legacy Personal Horoscopes (if any exist) */}
            {dashboardData.personalHoroscopes && dashboardData.personalHoroscopes.length > 0 && (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-gray-400 mb-4">
                    <TranslatedText text="Legacy Personal Messages" />
                  </h3>
                </div>
                {dashboardData.personalHoroscopes.map((horoscope: any, index: number) => (
                  <div key={horoscope.id} className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                    <h4 className="text-md font-semibold text-white mb-2 flex items-start">
                      <BookOpen className="mr-2 text-blue-400 flex-shrink-0 mt-0.5" size={16} />
                      <span className="leading-tight">
                        <TranslatedText text={horoscope.title} />
                      </span>
                    </h4>
                    <p className="text-gray-300 leading-relaxed text-sm">
                      <TranslatedText text={horoscope.content} />
                    </p>
                    <div className="mt-2 text-xs text-gray-500">
                      {new Date(horoscope.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'guide' && (
          dashboardData.dailyReading ? (
          <div className="space-y-4">
            {/* Daily Reading Header */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
              <h2 className="text-lg font-bold text-white mb-2 flex items-center">
                <Clock className="mr-2 text-green-400 flex-shrink-0" size={18} />
                {t('today_cosmic_guide')}
              </h2>
              <div className="flex flex-col space-y-1 text-sm text-gray-300">
                <span>📅 {new Date(dashboardData.dailyReading.date).toLocaleDateString()}</span>
                <span className="flex items-center">
                  <Smile className="mr-1" size={14} />
                  <TranslatedText
                    text={dashboardData.dailyReading.mood}
                    translations={getDailyReadingTranslations('mood')}
                    fallback={dashboardData.dailyReading.mood}
                  />
                </span>
              </div>
            </div>

            {/* Readings Cards */}
            <div className="space-y-4">
              {/* General Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
                <h3 className="text-base font-bold text-white mb-3 flex items-center">
                  <Star className="mr-2 text-yellow-400 flex-shrink-0" size={16} />
                  {t('general_reading')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.generalReading}
                    translations={getDailyReadingTranslations('generalReading')}
                    fallback={dashboardData.dailyReading.generalReading}
                  />
                </p>
              </div>

              {/* Love Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
                <h3 className="text-base font-bold text-white mb-3 flex items-center">
                  <Heart className="mr-2 text-pink-400 flex-shrink-0" size={16} />
                  {t('love_relationships')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.loveReading}
                    translations={getDailyReadingTranslations('loveReading')}
                    fallback={dashboardData.dailyReading.loveReading}
                  />
                </p>
              </div>

              {/* Career Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
                <h3 className="text-base font-bold text-white mb-3 flex items-center">
                  <Briefcase className="mr-2 text-blue-400 flex-shrink-0" size={16} />
                  {t('career_money')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.careerReading}
                    translations={getDailyReadingTranslations('careerReading')}
                    fallback={dashboardData.dailyReading.careerReading}
                  />
                </p>
              </div>

              {/* Health Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
                <h3 className="text-base font-bold text-white mb-3 flex items-center">
                  <Activity className="mr-2 text-green-400 flex-shrink-0" size={16} />
                  {t('health_wellness')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.healthReading}
                    translations={getDailyReadingTranslations('healthReading')}
                    fallback={dashboardData.dailyReading.healthReading}
                  />
                </p>
              </div>
            </div>

            {/* Lucky Elements - Mobile Optimized */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
              <h3 className="text-lg font-bold text-white mb-4">
                {t('todays_lucky_elements')}
              </h3>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/5 rounded-lg p-3 text-center">
                  <Hash className="text-yellow-400 mx-auto mb-2" size={18} />
                  <h4 className="text-white font-semibold text-xs mb-1">
                    {t('lucky_number')}
                  </h4>
                  <p className="text-xl font-bold text-yellow-400">
                    {dashboardData.dailyReading.luckyNumber}
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-3 text-center">
                  <Palette className="text-pink-400 mx-auto mb-2" size={18} />
                  <h4 className="text-white font-semibold text-xs mb-1">
                    {t('lucky_color')}
                  </h4>
                  <p className="text-sm font-semibold text-pink-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyColor}
                      translations={getDailyReadingTranslations('luckyColor')}
                      fallback={dashboardData.dailyReading.luckyColor}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-3 text-center">
                  <Clock className="text-blue-400 mx-auto mb-2" size={18} />
                  <h4 className="text-white font-semibold text-xs mb-1">
                    {t('lucky_time')}
                  </h4>
                  <p className="text-xs font-semibold text-blue-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyTime}
                      translations={getDailyReadingTranslations('luckyTime')}
                      fallback={dashboardData.dailyReading.luckyTime}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-3 text-center">
                  <Gem className="text-purple-400 mx-auto mb-2" size={18} />
                  <h4 className="text-white font-semibold text-xs mb-1">
                    {t('lucky_gem')}
                  </h4>
                  <p className="text-xs font-semibold text-purple-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyGem}
                      translations={getDailyReadingTranslations('luckyGem')}
                      fallback={dashboardData.dailyReading.luckyGem}
                    />
                  </p>
                </div>
              </div>

              {/* Daily Advice */}
              <div className="bg-white/5 rounded-lg p-3 mb-3">
                <h4 className="text-white font-semibold mb-2 flex items-center">
                  <BookOpen className="mr-2 text-green-400" size={16} />
                  {t('daily_advice')}
                </h4>
                <p className="text-gray-200 leading-relaxed text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.advice}
                    translations={getDailyReadingTranslations('advice')}
                    fallback={dashboardData.dailyReading.advice}
                  />
                </p>
              </div>

              {/* Compatibility */}
              <div className="bg-white/5 rounded-lg p-3">
                <h4 className="text-white font-semibold mb-2 flex items-center">
                  <Users className="mr-2 text-orange-400" size={16} />
                  {t('compatible_signs_today')}
                </h4>
                <p className="text-orange-300 font-medium text-sm">
                  <TranslatedText
                    text={dashboardData.dailyReading.compatibility}
                    translations={getDailyReadingTranslations('compatibility')}
                    fallback={dashboardData.dailyReading.compatibility}
                  />
                </p>
              </div>
            </div>
          </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
              <Clock className="mx-auto mb-4 text-gray-400" size={40} />
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('no_daily_guide_available')}
              </h3>
              <p className="text-gray-300 text-sm">
                {t('daily_guide_check_back')}
              </p>
            </div>
          )
        )}
      </main>

      {/* Desktop Content */}
      <main className="hidden md:block max-w-6xl mx-auto px-4 py-8 pb-8 relative z-[10]" style={{ display: 'block', visibility: 'visible' }}>
        {activeTab === 'horoscope' && (
          <div className="space-y-6" style={{ display: 'block', visibility: 'visible' }}>
            {/* Birth Chart (Handahana) - New Calculated System */}
            {dashboardData.birthChart ? (
              <div style={{ display: 'block', visibility: 'visible', width: '100%' }}>
                <BirthChart birthChart={dashboardData.birthChart} />
              </div>
            ) : (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
                <Star className="mx-auto mb-6 text-yellow-400" size={64} />
                <h3 className="text-2xl font-semibold text-white mb-4">
                  <TranslatedText text="Your Birth Chart (Handahana) Awaits" />
                </h3>
                <p className="text-gray-300 text-lg mb-6 max-w-2xl mx-auto">
                  <TranslatedText text="Discover your cosmic blueprint with a personalized Vedic astrology birth chart. Get detailed interpretations of your planetary positions, houses, and life guidance based on your exact birth time and location." />
                </p>
                {user?.birthTime && user?.birthPlace ? (
                  <button
                    onClick={handleCalculateBirthChart}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-200 shadow-lg text-lg"
                  >
                    <TranslatedText text="Calculate My Birth Chart" />
                  </button>
                ) : (
                  <div className="text-center">
                    <p className="text-yellow-300 text-lg mb-4">
                      <TranslatedText text="Birth time and place are required for accurate calculations." />
                    </p>
                    <p className="text-gray-400">
                      <TranslatedText text="Please contact admin to update your birth details." />
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Legacy Personal Horoscopes (if any exist) */}
            {dashboardData.personalHoroscopes && dashboardData.personalHoroscopes.length > 0 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-400 mb-6">
                    <TranslatedText text="Legacy Personal Messages" />
                  </h3>
                </div>
                {dashboardData.personalHoroscopes.map((horoscope: any, index: number) => (
                  <div key={horoscope.id} className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
                    <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                      <BookOpen className="mr-2 text-blue-400 flex-shrink-0" size={20} />
                      <TranslatedText text={horoscope.title} />
                    </h4>
                    <p className="text-gray-300 leading-relaxed text-lg">
                      <TranslatedText text={horoscope.content} />
                    </p>
                    <div className="mt-4 text-sm text-gray-500">
                      {new Date(horoscope.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'guide' && (
          dashboardData.dailyReading ? (
          <div className="space-y-6">
            {/* Daily Reading Header */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-2 flex items-center">
                <Clock className="mr-2 text-green-400 flex-shrink-0" size={20} />
                {t('today_cosmic_guide')}
              </h2>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm text-gray-300">
                <span>📅 {new Date(dashboardData.dailyReading.date).toLocaleDateString()}</span>
                <span className="flex items-center">
                  <Smile className="mr-1" size={16} />
                  <TranslatedText
                    text={dashboardData.dailyReading.mood}
                    translations={getDailyReadingTranslations('mood')}
                    fallback={dashboardData.dailyReading.mood}
                  />
                </span>
              </div>
            </div>

            {/* Readings Grid */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* General Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Star className="mr-2 text-yellow-400 flex-shrink-0" size={18} />
                  {t('general_reading')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-base">
                  <TranslatedText
                    text={dashboardData.dailyReading.generalReading}
                    translations={getDailyReadingTranslations('generalReading')}
                    fallback={dashboardData.dailyReading.generalReading}
                  />
                </p>
              </div>

              {/* Love Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Heart className="mr-2 text-pink-400 flex-shrink-0" size={18} />
                  {t('love_relationships')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-base">
                  <TranslatedText
                    text={dashboardData.dailyReading.loveReading}
                    translations={getDailyReadingTranslations('loveReading')}
                    fallback={dashboardData.dailyReading.loveReading}
                  />
                </p>
              </div>

              {/* Career Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Briefcase className="mr-2 text-blue-400 flex-shrink-0" size={18} />
                  {t('career_money')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-base">
                  <TranslatedText
                    text={dashboardData.dailyReading.careerReading}
                    translations={getDailyReadingTranslations('careerReading')}
                    fallback={dashboardData.dailyReading.careerReading}
                  />
                </p>
              </div>

              {/* Health Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Activity className="mr-2 text-green-400" size={20} />
                  {t('health_wellness')}
                </h3>
                <p className="text-gray-200 leading-relaxed text-base">
                  <TranslatedText
                    text={dashboardData.dailyReading.healthReading}
                    translations={getDailyReadingTranslations('healthReading')}
                    fallback={dashboardData.dailyReading.healthReading}
                  />
                </p>
              </div>
            </div>

            {/* Lucky Elements */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-4">
                {t('todays_lucky_elements')}
              </h3>

              <div className="grid grid-cols-4 gap-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Hash className="text-yellow-400 mx-auto mb-2" size={20} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    {t('lucky_number')}
                  </h4>
                  <p className="text-2xl font-bold text-yellow-400">
                    {dashboardData.dailyReading.luckyNumber}
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Palette className="text-pink-400 mx-auto mb-2" size={20} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    {t('lucky_color')}
                  </h4>
                  <p className="text-lg font-semibold text-pink-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyColor}
                      translations={getDailyReadingTranslations('luckyColor')}
                      fallback={dashboardData.dailyReading.luckyColor}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Clock className="text-blue-400 mx-auto mb-2" size={20} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    {t('lucky_time')}
                  </h4>
                  <p className="text-sm font-semibold text-blue-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyTime}
                      translations={getDailyReadingTranslations('luckyTime')}
                      fallback={dashboardData.dailyReading.luckyTime}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Gem className="text-purple-400 mx-auto mb-2" size={24} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    {t('lucky_gem')}
                  </h4>
                  <p className="text-sm font-semibold text-purple-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyGem}
                      translations={getDailyReadingTranslations('luckyGem')}
                      fallback={dashboardData.dailyReading.luckyGem}
                    />
                  </p>
                </div>
              </div>

              {/* Daily Advice */}
              <div className="bg-white/5 rounded-lg p-4 mb-4">
                <h4 className="text-white font-semibold mb-3 flex items-center">
                  <BookOpen className="mr-2 text-green-400" size={20} />
                  {t('daily_advice')}
                </h4>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.advice}
                    translations={getDailyReadingTranslations('advice')}
                    fallback={dashboardData.dailyReading.advice}
                  />
                </p>
              </div>

              {/* Compatibility */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-semibold mb-3 flex items-center">
                  <Users className="mr-2 text-orange-400" size={20} />
                  {t('compatible_signs_today')}
                </h4>
                <p className="text-orange-300 font-medium">
                  <TranslatedText
                    text={dashboardData.dailyReading.compatibility}
                    translations={getDailyReadingTranslations('compatibility')}
                    fallback={dashboardData.dailyReading.compatibility}
                  />
                </p>
              </div>
            </div>
          </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
              <Clock className="mx-auto mb-4 text-gray-400" size={48} />
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('no_daily_guide_available')}
              </h3>
              <p className="text-gray-300">
                {t('daily_guide_check_back')}
              </p>
            </div>
          )
        )}
      </main>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
