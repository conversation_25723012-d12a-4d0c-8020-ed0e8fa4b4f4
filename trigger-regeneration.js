// Simple script to trigger regeneration for testing
const fetch = require('node-fetch');

async function triggerRegeneration() {
  try {
    console.log('🔄 Attempting to trigger regeneration...');
    
    // First, let's try to login as admin to get a token
    const loginResponse = await fetch('http://localhost:3000/api/admin/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginResponse.status);
      return;
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    
    // Extract token from response
    const token = loginData.token;
    if (!token) {
      console.log('❌ No token received');
      return;
    }
    
    // Now trigger regeneration
    const regenResponse = await fetch('http://localhost:3000/api/admin/birth-charts/regenerate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        userId: 'cmcz67f210000pgg409ks9sly'
      })
    });
    
    if (!regenResponse.ok) {
      console.log('❌ Regeneration failed:', regenResponse.status);
      return;
    }
    
    console.log('✅ Regeneration triggered successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

triggerRegeneration();
