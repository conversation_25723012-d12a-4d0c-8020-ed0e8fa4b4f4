// Test script to verify the chart fix
const { calculateLagna<PERSON>hart } = require('./src/lib/astrology.ts');

// Mock house data based on the logs
const mockHouses = [
  { number: 1, sign: "Libra", cusp: 180, lord: "Venus" },
  { number: 2, sign: "<PERSON><PERSON><PERSON>", cusp: 210, lord: "Mars" },
  { number: 3, sign: "Sagittarius", cusp: 240, lord: "Jupiter" },
  { number: 4, sign: "Capricorn", cusp: 270, lord: "Saturn" },
  { number: 5, sign: "Aquarius", cusp: 300, lord: "Saturn" },
  { number: 6, sign: "<PERSON><PERSON><PERSON>", cusp: 330, lord: "Jupiter" },
  { number: 7, sign: "<PERSON><PERSON>", cusp: 0, lord: "Mars" },
  { number: 8, sign: "Taurus", cusp: 30, lord: "Venus" },
  { number: 9, sign: "Gemini", cusp: 60, lord: "Mercury" },
  { number: 10, sign: "Cancer", cusp: 90, lord: "Moon" },
  { number: 11, sign: "<PERSON>", cusp: 120, lord: "<PERSON>" },
  { number: 12, sign: "<PERSON><PERSON><PERSON>", cusp: 150, lord: "<PERSON>" }
];

// Mock planet data
const mockPlanets = [
  { name: "<PERSON><PERSON>", longitude: 256.0205, sign: "Sagittarius", house: 3 },
  { name: "Chandra", longitude: 300.0467, sign: "Aquarius", house: 5 }
];

console.log('🧪 Testing chart fix...');
console.log('Mock houses:', mockHouses.slice(0, 3));

try {
  const result = calculateLagnaChart(mockPlanets, mockHouses);
  console.log('✅ Chart calculation result:');
  console.log('Houses with signs:', result.houses.slice(0, 5).map(h => ({ 
    house: h.houseNumber, 
    sign: h.sign, 
    signShort: h.signShort 
  })));
  
  if (result.houses[0].sign !== 'Unknown') {
    console.log('🎉 SUCCESS: Signs are now properly mapped!');
  } else {
    console.log('❌ FAILED: Signs are still showing as Unknown');
  }
} catch (error) {
  console.error('❌ Error testing chart:', error);
}
