// Astrology Calculation Service
// Handahana (Birth Chart) calculations using Vedic Astrology principles

import { Origin, Horoscope } from 'circular-natal-horoscope-js';

export interface BirthDetails {
  birthDate: Date;
  birthTime: string; // "HH:MM" format
  birthPlace: string;
  latitude: number;
  longitude: number;
}

export interface PlanetPosition {
  name: string;
  longitude: number;
  latitude: number;
  sign: string;
  house: number;
  nakshatra: string;
  isRetrograde: boolean;
}

export interface HouseInfo {
  number: number;
  sign: string;
  cusp: number;
  lord: string;
}

export interface BirthChartData {
  // Basic Info
  ascendant: string;
  moonSign: string;
  sunSign: string;

  // Planetary Positions
  planets: PlanetPosition[];

  // Houses
  houses: HouseInfo[];

  // Aspects
  aspects: any[];

  // Nakshatras
  nakshatras: any[];

  // Dashas (Planetary Periods)
  dashas: any[];

  // Enhanced Vedic Chart Data
  lagnaChart?: VedicChart;
  navamsaChart?: VedicChart;
  chandraChart?: VedicChart;
  karakTable?: KarakData;
  avasthaTable?: AvasthaData;
  planetaryDetails?: PlanetaryDetail[];
  vimshottariDasha?: VimshottariDashaData;
  ashtakavarga?: AshtakavargaData;
}

// New interfaces for enhanced Vedic calculations
export interface VedicChart {
  houses: ChartHouse[];
  ascendantHouse: number;
}

export interface ChartHouse {
  houseNumber: number;
  sign: string;
  signShort: string;
  planets: ChartPlanet[];
}

export interface ChartPlanet {
  name: string;
  symbol: string;
  longitude: number;
  retrograde?: boolean;
}

export interface KarakData {
  [karak: string]: {
    sthir: string;
    chara: string;
  };
}

export interface AvasthaData {
  [planet: string]: {
    jagrat: string;
    baladi: string;
    deeptadi: string;
  };
}

export interface PlanetaryDetail {
  planet: string;
  combust: boolean;
  retrograde: boolean;
  rashi: string;
  longitude: string;
  nakshatra: string;
  pada: number;
  relation: string;
}

export interface VimshottariDashaData {
  currentDasha: string;
  balance: string;
  periods: DashaPeriod[];
}

export interface DashaPeriod {
  planet: string;
  startDate: string;
  endDate: string;
}

export interface AshtakavargaData {
  sunTable: number[][];
  moonTable: number[][];
  marsTable: number[][];
  mercuryTable: number[][];
  jupiterTable: number[][];
  venusTable: number[][];
  saturnTable: number[][];
  ascendantTable: number[][];
  totalTable: number[][];
  sarvashtakavarga: number[];
}

// Vedic Zodiac Signs (Sidereal)
export const VEDIC_SIGNS = [
  'Aries', 'Taurus', 'Gemini', 'Cancer',
  'Leo', 'Virgo', 'Libra', 'Scorpio',
  'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
];

// Nakshatras (27 Lunar Mansions)
export const NAKSHATRAS = [
  'Ashwini', 'Bharani', 'Krittika', 'Rohini', 'Mrigashira', 'Ardra',
  'Punarvasu', 'Pushya', 'Ashlesha', 'Magha', 'Purva Phalguni', 'Uttara Phalguni',
  'Hasta', 'Chitra', 'Swati', 'Vishakha', 'Anuradha', 'Jyeshtha',
  'Mula', 'Purva Ashadha', 'Uttara Ashadha', 'Shravana', 'Dhanishta', 'Shatabhisha',
  'Purva Bhadrapada', 'Uttara Bhadrapada', 'Revati'
];

// Additional Vedic Astrology Constants for Chart Display
export const VEDIC_SIGNS_SHORT = [
  'Ar', 'Ta', 'Ge', 'Ca', 'Le', 'Vi',
  'Li', 'Sc', 'Sa', 'Cp', 'Aq', 'Pi'
];

export const PLANET_SYMBOLS = {
  'Sun': 'Su',
  'Moon': 'Mo',
  'Mars': 'Ma',
  'Mercury': 'Me',
  'Jupiter': 'Ju',
  'Venus': 'Ve',
  'Saturn': 'Sa',
  'Rahu': 'Ra',
  'Ketu': 'Ke',
  'Uranus': 'Ur',
  'Neptune': 'Ne',
  'Pluto': 'Pl'
};

// Function to get translated planet symbols
export function getPlanetSymbol(planetName: string, t?: (key: string) => string): string {
  if (!t) {
    return PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || planetName.substring(0, 2);
  }

  const symbolMap: { [key: string]: string } = {
    'Sun': t('sun_symbol'),
    'Moon': t('moon_symbol'),
    'Mars': t('mars_symbol'),
    'Mercury': t('mercury_symbol'),
    'Jupiter': t('jupiter_symbol'),
    'Venus': t('venus_symbol'),
    'Saturn': t('saturn_symbol'),
    'Rahu': t('rahu_symbol'),
    'Ketu': t('ketu_symbol'),
    'Uranus': t('uranus_symbol'),
    'Neptune': t('neptune_symbol'),
    'Pluto': t('pluto_symbol')
  };

  return symbolMap[planetName] || PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || planetName.substring(0, 2);
}

// Function to update chart symbols with translations
export function updateChartSymbols(chart: VedicChart, t: (key: string) => string): VedicChart {
  if (!chart || !chart.houses) return chart;

  return {
    ...chart,
    houses: chart.houses.map(house => ({
      ...house,
      planets: house.planets.map(planet => ({
        ...planet,
        symbol: getPlanetSymbol(planet.name, t)
      }))
    }))
  };
}

// Nakshatra Lords for Vimshottari Dasha
export const NAKSHATRA_LORDS = [
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury',
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury',
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'
];

// Vimshottari Dasha periods in years
export const DASHA_PERIODS = {
  'Ketu': 7, 'Venus': 20, 'Sun': 6, 'Moon': 10, 'Mars': 7,
  'Rahu': 18, 'Jupiter': 16, 'Saturn': 19, 'Mercury': 17
};

// Karak (Significator) relationships
export const KARAK_RELATIONSHIPS = {
  'Atma': ['Sun', 'Moon'],
  'Amatya': ['Mercury', 'Mars'],
  'Bhratru': ['Mars', 'Saturn'],
  'Matru': ['Moon', 'Jupiter'],
  'Putra': ['Jupiter', 'Mercury'],
  'Gnati': ['Saturn', 'Sun'],
  'Dara': ['Venus', 'Venus']
};

// Avastha (Planetary States)
export const AVASTHA_STATES = {
  'Sun': { jagrat: 'Swapna', baladi: 'Yuva', deeptadi: 'Deepta' },
  'Moon': { jagrat: 'Jaagrat', baladi: 'Bala', deeptadi: 'Swatha' },
  'Mars': { jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Deepta' },
  'Mercury': { jagrat: 'Swapna', baladi: 'Kumar', deeptadi: 'Shant' },
  'Jupiter': { jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Khal' },
  'Venus': { jagrat: 'Susupta', baladi: 'Mrat', deeptadi: 'Muditha' },
  'Saturn': { jagrat: 'Susupta', baladi: 'Vradha', deeptadi: 'Deepta' }
};

// Planet names in Vedic Astrology
export const VEDIC_PLANETS = {
  sun: 'Surya',
  moon: 'Chandra',
  mercury: 'Budha',
  venus: 'Shukra',
  mars: 'Mangal',
  jupiter: 'Guru',
  saturn: 'Shani',
  rahu: 'Rahu', // North Node
  ketu: 'Ketu'  // South Node
};

/**
 * Calculate birth chart using Vedic Astrology principles
 */
export async function calculateBirthChart(birthDetails: BirthDetails): Promise<BirthChartData> {
  try {
    console.log('🔮 Calculating birth chart for:', birthDetails);

    // Validate birth details
    if (!birthDetails.birthDate || !birthDetails.birthTime ||
        birthDetails.latitude === undefined || birthDetails.longitude === undefined) {
      throw new Error('Invalid birth details: missing required fields');
    }

    // Parse birth time
    const [hours, minutes] = birthDetails.birthTime.split(':').map(Number);

    // Validate time
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw new Error('Invalid birth time format. Expected HH:MM format');
    }
    
    // Create Origin object for calculations
    const origin = new Origin({
      year: birthDetails.birthDate.getFullYear(),
      month: birthDetails.birthDate.getMonth(), // Use 0-based month (0-11) as expected by the library
      date: birthDetails.birthDate.getDate(),
      hour: hours,
      minute: minutes,
      latitude: birthDetails.latitude,
      longitude: birthDetails.longitude
    });

    // Create Horoscope with Sidereal (Vedic) zodiac
    console.log('📊 Creating horoscope with origin:', origin);
    let horoscope;
    try {
      horoscope = new Horoscope({
        origin: origin,
        houseSystem: 'whole-sign', // Traditional Vedic house system
        zodiac: 'sidereal', // Vedic uses sidereal zodiac
        aspectPoints: ['bodies', 'points', 'angles'],
        aspectWithPoints: ['bodies', 'points', 'angles'],
        aspectTypes: ['major', 'minor'],
        customOrbs: {},
        language: 'en'
      });
      console.log('✅ Horoscope created successfully');
    } catch (horoscopeError) {
      console.error('❌ Error creating horoscope:', horoscopeError);
      throw new Error(`Failed to create horoscope: ${horoscopeError instanceof Error ? horoscopeError.message : 'Unknown error'}`);
    }

    // Extract planetary positions
    const planets: PlanetPosition[] = [];

    // Get celestial bodies for later use
    const celestialBodies = horoscope.CelestialBodies;

    // Process major planets
    try {
      console.log('📊 Processing celestial bodies:', Object.keys(celestialBodies));

      for (const [planetKey, planetData] of Object.entries(celestialBodies)) {
        if (planetKey === 'all') continue;

        try {
          const planet = planetData as any;
          if (planet && planet.ChartPosition && planet.ChartPosition.Ecliptic) {
            planets.push({
              name: VEDIC_PLANETS[planetKey as keyof typeof VEDIC_PLANETS] || planetKey,
              longitude: planet.ChartPosition.Ecliptic.DecimalDegrees,
              latitude: 0, // Will be calculated separately if needed
              sign: getVedicSign(planet.ChartPosition.Ecliptic.DecimalDegrees),
              house: getHouseFromDegree(planet.ChartPosition.Horizon?.DecimalDegrees || 0),
              nakshatra: getNakshatra(planet.ChartPosition.Ecliptic.DecimalDegrees),
              isRetrograde: planet.isRetrograde || false
            });
            console.log(`✅ Processed planet: ${planetKey}`);
          } else {
            console.warn(`⚠️ Skipping planet ${planetKey}: missing chart position data`);
          }
        } catch (planetError) {
          console.error(`❌ Error processing planet ${planetKey}:`, planetError);
        }
      }
    } catch (bodiesError) {
      console.error('❌ Error processing celestial bodies:', bodiesError);
      throw new Error(`Failed to process celestial bodies: ${bodiesError instanceof Error ? bodiesError.message : 'Unknown error'}`);
    }

    // Process Lunar Nodes (Rahu/Ketu)
    const celestialPoints = horoscope.CelestialPoints;
    if (celestialPoints.northnode) {
      const rahu = celestialPoints.northnode as any;
      planets.push({
        name: 'Rahu',
        longitude: rahu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(rahu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    if (celestialPoints.southnode) {
      const ketu = celestialPoints.southnode as any;
      planets.push({
        name: 'Ketu',
        longitude: ketu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(ketu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    // Extract house information
    const houses: HouseInfo[] = [];
    const houseData = horoscope.Houses;
    console.log('🏠 House data structure (updated):', JSON.stringify(houseData, null, 2));

    for (let i = 0; i < 12; i++) {
      if (houseData[i]) {
        const house = houseData[i] as any;
        console.log(`🏠 House ${i + 1} structure:`, JSON.stringify(house, null, 2));

        // Extract decimal degrees from the JSON structure
        let decimalDegrees: number = 0;
        try {
          // Convert to JSON and back to get plain object
          const houseJson = JSON.parse(JSON.stringify(house));

          if (houseJson.ChartPosition?.StartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.ChartPosition.StartPosition.Ecliptic.DecimalDegrees;
          } else if (houseJson.ChartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.ChartPosition.Ecliptic.DecimalDegrees;
          } else if (houseJson.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.DecimalDegrees;
          } else if (houseJson.Longitude !== undefined) {
            decimalDegrees = houseJson.Longitude;
          } else {
            console.warn(`⚠️ Could not find decimal degrees for house ${i + 1}:`, houseJson);
            decimalDegrees = 0; // Default fallback
          }
        } catch (error) {
          console.error(`❌ Error processing house ${i + 1}:`, error);
          decimalDegrees = 0;
        }

        houses.push({
          number: i + 1,
          sign: getVedicSign(decimalDegrees),
          cusp: decimalDegrees,
          lord: getHouseLord(getVedicSign(decimalDegrees))
        });
      }
    }

    // Get ascendant, moon sign, and sun sign with safe access
    console.log('🌅 Ascendant structure:', JSON.stringify(horoscope.Ascendant, null, 2));
    console.log('🌙 Moon structure:', JSON.stringify(celestialBodies.moon, null, 2));
    console.log('☀️ Sun structure:', JSON.stringify(celestialBodies.sun, null, 2));

    const getDecimalDegrees = (obj: any): number => {
      try {
        // Convert to JSON and back to get plain object
        const objJson = JSON.parse(JSON.stringify(obj));

        if (objJson?.ChartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
          return objJson.ChartPosition.Ecliptic.DecimalDegrees;
        } else if (objJson?.DecimalDegrees !== undefined) {
          return objJson.DecimalDegrees;
        } else if (objJson?.Longitude !== undefined) {
          return objJson.Longitude;
        }
      } catch (error) {
        console.error('Error extracting decimal degrees:', error);
      }
      return 0; // Default fallback
    };

    const ascendant = horoscope.Ascendant ? getVedicSign(getDecimalDegrees(horoscope.Ascendant)) : 'Unknown';
    const moonSign = celestialBodies.moon ? getVedicSign(getDecimalDegrees(celestialBodies.moon)) : 'Unknown';
    const sunSign = celestialBodies.sun ? getVedicSign(getDecimalDegrees(celestialBodies.sun)) : 'Unknown';

    // Get aspects
    const aspects = horoscope.Aspects.all || [];

    // Calculate Nakshatras for all planets
    const nakshatras = planets.map(planet => ({
      planet: planet.name,
      nakshatra: planet.nakshatra,
      longitude: planet.longitude
    }));

    // Calculate basic Dashas (simplified)
    const dashas = calculateBasicDashas(celestialBodies.moon ? (celestialBodies.moon as any).ChartPosition.Ecliptic.DecimalDegrees : 0);

    const birthChartData: BirthChartData = {
      ascendant,
      moonSign,
      sunSign,
      planets,
      houses,
      aspects,
      nakshatras,
      dashas
    };

    console.log('✅ Birth chart calculated successfully');
    return birthChartData;

  } catch (error) {
    console.error('❌ Error calculating birth chart:', error);
    throw new Error(`Failed to calculate birth chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Vedic zodiac sign from longitude
 */
function getVedicSign(longitude: number): string {
  const signIndex = Math.floor(longitude / 30);
  return VEDIC_SIGNS[signIndex] || 'Unknown';
}

/**
 * Get house number from horizon degree
 */
function getHouseFromDegree(horizonDegree: number): number {
  // Normalize to 0-360 range
  let degree = horizonDegree;
  while (degree < 0) degree += 360;
  while (degree >= 360) degree -= 360;
  
  // Calculate house (1-12)
  const house = Math.floor(degree / 30) + 1;
  return house > 12 ? house - 12 : house;
}

/**
 * Get Nakshatra from longitude
 */
function getNakshatra(longitude: number): string {
  // Each nakshatra spans 13.333... degrees (360/27)
  const nakshatraIndex = Math.floor(longitude / (360 / 27));
  return NAKSHATRAS[nakshatraIndex] || 'Unknown';
}

/**
 * Get house lord for a sign
 */
function getHouseLord(sign: string): string {
  const lords: { [key: string]: string } = {
    'Aries': 'Mars',
    'Taurus': 'Venus',
    'Gemini': 'Mercury',
    'Cancer': 'Moon',
    'Leo': 'Sun',
    'Virgo': 'Mercury',
    'Libra': 'Venus',
    'Scorpio': 'Mars',
    'Sagittarius': 'Jupiter',
    'Capricorn': 'Saturn',
    'Aquarius': 'Saturn',
    'Pisces': 'Jupiter'
  };
  return lords[sign] || 'Unknown';
}

/**
 * Calculate basic Dasha periods (simplified Vimshottari Dasha)
 */
function calculateBasicDashas(moonLongitude: number): any[] {
  // This is a simplified version - real Dasha calculations are much more complex
  const nakshatra = getNakshatra(moonLongitude);
  const nakshatraIndex = NAKSHATRAS.indexOf(nakshatra);
  
  // Vimshottari Dasha periods in years
  const dashaPeriods = [
    { planet: 'Ketu', years: 7 },
    { planet: 'Venus', years: 20 },
    { planet: 'Sun', years: 6 },
    { planet: 'Moon', years: 10 },
    { planet: 'Mars', years: 7 },
    { planet: 'Rahu', years: 18 },
    { planet: 'Jupiter', years: 16 },
    { planet: 'Saturn', years: 19 },
    { planet: 'Mercury', years: 17 }
  ];
  
  // Start from the nakshatra lord
  const startIndex = nakshatraIndex % 9;
  const dashas = [];
  
  for (let i = 0; i < 9; i++) {
    const index = (startIndex + i) % 9;
    dashas.push(dashaPeriods[index]);
  }
  
  return dashas;
}

/**
 * Calculate Enhanced Vedic Birth Chart with all divisional charts and tables
 */
export async function calculateEnhancedBirthChart(birthDetails: BirthDetails): Promise<BirthChartData> {
  console.log('🔮 Starting enhanced birth chart calculation...');

  try {
    // Get basic birth chart data first
    const basicChart = await calculateBirthChart(birthDetails);
    console.log('✅ Basic chart calculated successfully');

    // Calculate enhanced Vedic charts and tables
    console.log('🔮 Calculating enhanced Vedic charts...');

    let lagnaChart, navamsaChart, chandraChart, karakTable, avasthaTable, planetaryDetails, vimshottariDasha, ashtakavarga;

    try {
      console.log('🔮 About to calculate Lagna Chart with planets:', basicChart.planets.length, 'houses:', basicChart.houses.length);
      console.log('🔮 Sample planet data:', JSON.stringify(basicChart.planets[0], null, 2));
      console.log('🔮 Sample house data:', JSON.stringify(basicChart.houses[0], null, 2));
      lagnaChart = calculateLagnaChart(basicChart.planets, basicChart.houses);
      console.log('✅ Lagna Chart calculated:', !!lagnaChart, 'houses count:', lagnaChart?.houses?.length);
      console.log('🔮 Lagna Chart result:', JSON.stringify(lagnaChart, null, 2));
    } catch (error) {
      console.error('❌ Error calculating Lagna Chart:', error);
      lagnaChart = null;
    }

    try {
      console.log('🔮 About to calculate Navamsa Chart');
      navamsaChart = calculateNavamsaChart(basicChart.planets);
      console.log('✅ Navamsa Chart calculated:', !!navamsaChart, 'houses count:', navamsaChart?.houses?.length);
    } catch (error) {
      console.error('❌ Error calculating Navamsa Chart:', error);
      navamsaChart = null;
    }

    try {
      console.log('🔮 About to calculate Chandra Chart with moonSign:', basicChart.moonSign);
      chandraChart = calculateChandraChart(basicChart.planets, basicChart.moonSign);
      console.log('✅ Chandra Chart calculated:', !!chandraChart, 'houses count:', chandraChart?.houses?.length);
    } catch (error) {
      console.error('❌ Error calculating Chandra Chart:', error);
      chandraChart = null;
    }

    try {
      console.log('🔮 About to calculate Karak Table');
      karakTable = calculateKarakTable(basicChart.planets);
      console.log('✅ Karak Table calculated:', !!karakTable, 'keys:', Object.keys(karakTable || {}).length);
    } catch (error) {
      console.error('❌ Error calculating Karak Table:', error);
      karakTable = null;
    }

    try {
      console.log('🔮 About to calculate Avastha Table');
      avasthaTable = calculateAvasthaTable(basicChart.planets);
      console.log('✅ Avastha Table calculated:', !!avasthaTable, 'keys:', Object.keys(avasthaTable || {}).length);
    } catch (error) {
      console.error('❌ Error calculating Avastha Table:', error);
      avasthaTable = null;
    }

    try {
      console.log('🔮 About to calculate Planetary Details');
      planetaryDetails = calculatePlanetaryDetails(basicChart.planets);
      console.log('✅ Planetary Details calculated:', !!planetaryDetails, 'planets count:', planetaryDetails?.length);
    } catch (error) {
      console.error('❌ Error calculating Planetary Details:', error);
      planetaryDetails = null;
    }

    try {
      console.log('🔮 About to calculate Vimshottari Dasha');
      vimshottariDasha = calculateVimshottariDasha(basicChart.planets, new Date(birthDetails.birthDate));
      console.log('✅ Vimshottari Dasha calculated:', !!vimshottariDasha, 'periods count:', vimshottariDasha?.periods?.length);
    } catch (error) {
      console.error('❌ Error calculating Vimshottari Dasha:', error);
      vimshottariDasha = null;
    }

    try {
      console.log('🔮 About to calculate Ashtakavarga');
      ashtakavarga = calculateAshtakavarga(basicChart.planets, basicChart.houses);
      console.log('✅ Ashtakavarga calculated:', !!ashtakavarga, 'has tables:', !!ashtakavarga?.sunTable);
    } catch (error) {
      console.error('❌ Error calculating Ashtakavarga:', error);
      ashtakavarga = null;
    }

    const enhancedChart = {
      ...basicChart,
      lagnaChart,
      navamsaChart,
      chandraChart,
      karakTable,
      avasthaTable,
      planetaryDetails,
      vimshottariDasha,
      ashtakavarga
    };

    console.log('✅ Enhanced birth chart calculation completed');
    return enhancedChart;
  } catch (error) {
    console.error('❌ Error in enhanced birth chart calculation:', error);
    throw error;
  }
}

/**
 * Calculate D1 Lagna Chart (Main Birth Chart)
 */
function calculateLagnaChart(planets: PlanetPosition[], houses: HouseInfo[], t?: (key: string) => string): VedicChart {
  console.log('🔥🔥🔥 LAGNA CHART CALCULATION STARTED - NEW CODE VERSION 🔥🔥🔥');
  const chartHouses: ChartHouse[] = [];

  // Initialize 12 houses
  for (let i = 1; i <= 12; i++) {
    // Try both data structures - the house data might have different formats
    const house = houses.find(h => h.id === i || h.number === i);
    const signKey = house?.Sign?.key || house?.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries',
      'taurus': 'Taurus',
      'gemini': 'Gemini',
      'cancer': 'Cancer',
      'leo': 'Leo',
      'virgo': 'Virgo',
      'libra': 'Libra',
      'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius',
      'capricorn': 'Capricorn',
      'aquarius': 'Aquarius',
      'pisces': 'Pisces'
    };

    // Handle both lowercase and capitalized sign names
    const normalizedSignKey = signKey.toLowerCase();
    const sign = signMapping[normalizedSignKey] || signKey || 'Unknown';
    const signIndex = VEDIC_SIGNS.indexOf(sign);
    const signShort = signIndex >= 0 ? VEDIC_SIGNS_SHORT[signIndex] : 'Un';

    // Debug logging
    console.log(`🏠 House ${i}: house=${JSON.stringify(house)}, signKey="${signKey}", mappedSign="${sign}", signIndex=${signIndex}, found=${signIndex >= 0}`);

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Place planets in houses
  planets.forEach(planet => {
    // Get house number from planet data structure
    const houseNumber = planet.House?.id || planet.house || 1;
    const houseIndex = houseNumber - 1;
    if (houseIndex >= 0 && houseIndex < 12) {
      const symbol = getPlanetSymbol(planet.name, t);
      chartHouses[houseIndex].planets.push({
        name: planet.name,
        symbol,
        longitude: planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0,
        retrograde: planet.retrograde || false
      });
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1
  };
}

/**
 * Calculate D9 Navamsa Chart
 */
function calculateNavamsaChart(planets: PlanetPosition[], t?: (key: string) => string): VedicChart {
  const chartHouses: ChartHouse[] = [];

  // Initialize 12 houses with proper signs
  for (let i = 1; i <= 12; i++) {
    const signIndex = (i - 1) % 12; // Houses 1-12 map to signs 0-11
    const sign = VEDIC_SIGNS[signIndex];
    const signShort = VEDIC_SIGNS_SHORT[signIndex];

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Calculate Navamsa positions for each planet
  planets.forEach((planet, planetIndex) => {
    try {
      // Get longitude from planet data structure
      const longitude = planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0;
      const navamsaLongitude = calculateNavamsaPosition(longitude);
      const navamsaSignIndex = Math.floor(navamsaLongitude / 30);

      // Ensure navamsaSignIndex is within bounds (0-11)
      const boundedSignIndex = Math.max(0, Math.min(11, navamsaSignIndex));
      const houseIndex = boundedSignIndex; // In Navamsa, sign index is the house index

      console.log(`🔍 Planet ${planet.name}: longitude=${longitude}, navamsaLongitude=${navamsaLongitude}, signIndex=${boundedSignIndex}, houseIndex=${houseIndex}`);

      const sign = VEDIC_SIGNS[boundedSignIndex] || 'Unknown';
      const signShort = VEDIC_SIGNS_SHORT[boundedSignIndex] || 'Un';
      const symbol = getPlanetSymbol(planet.name, t);

      // Debug: Check chartHouses array
      console.log(`🏠 chartHouses length: ${chartHouses.length}, houseIndex: ${houseIndex}, exists: ${!!chartHouses[houseIndex]}`);

      // Ensure houseIndex is within bounds and chartHouses exists
      if (houseIndex >= 0 && houseIndex < chartHouses.length && chartHouses[houseIndex]) {
        if (!chartHouses[houseIndex].sign) {
          chartHouses[houseIndex].sign = sign;
          chartHouses[houseIndex].signShort = signShort;
        }

        chartHouses[houseIndex].planets.push({
          name: planet.name,
          symbol,
          longitude: navamsaLongitude,
          retrograde: planet.retrograde || false
        });

        console.log(`✅ Added ${planet.name} to house ${houseIndex + 1} (${sign})`);
      } else {
        console.error(`❌ Invalid houseIndex ${houseIndex} for planet ${planet.name}`);
      }
    } catch (error) {
      console.error(`❌ Error processing planet ${planet.name}:`, error);
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1
  };
}

/**
 * Calculate Navamsa position from longitude
 */
function calculateNavamsaPosition(longitude: number): number {
  const sign = Math.floor(longitude / 30);
  const degreeInSign = longitude % 30;
  const navamsaInSign = Math.floor(degreeInSign / (30/9));

  // Navamsa calculation based on sign type (movable, fixed, dual)
  let navamsaSign: number;
  if ([0, 3, 6, 9].includes(sign)) { // Movable signs (Aries, Cancer, Libra, Capricorn)
    navamsaSign = (sign + navamsaInSign) % 12;
  } else if ([1, 4, 7, 10].includes(sign)) { // Fixed signs (Taurus, Leo, Scorpio, Aquarius)
    navamsaSign = (sign + 8 + navamsaInSign) % 12;
  } else { // Dual signs (Gemini, Virgo, Sagittarius, Pisces)
    navamsaSign = (sign + 4 + navamsaInSign) % 12;
  }

  return navamsaSign * 30 + (degreeInSign % (30/9)) * 9;
}

/**
 * Calculate Chandra Chart (Moon-based chart)
 */
function calculateChandraChart(planets: PlanetPosition[], moonSign: string, t?: (key: string) => string): VedicChart {
  const moonSignIndex = VEDIC_SIGNS.indexOf(moonSign);
  const chartHouses: ChartHouse[] = [];

  // Initialize 12 houses with Moon's sign as 1st house
  for (let i = 1; i <= 12; i++) {
    const signIndex = (moonSignIndex + i - 1) % 12;
    const sign = VEDIC_SIGNS[signIndex];
    const signShort = VEDIC_SIGNS_SHORT[signIndex];

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Place planets relative to Moon's position
  planets.forEach(planet => {
    // Get sign from planet data structure
    const planetSignKey = planet.Sign?.key || planet.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries', 'taurus': 'Taurus', 'gemini': 'Gemini', 'cancer': 'Cancer',
      'leo': 'Leo', 'virgo': 'Virgo', 'libra': 'Libra', 'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius', 'capricorn': 'Capricorn', 'aquarius': 'Aquarius', 'pisces': 'Pisces'
    };

    const planetSign = signMapping[planetSignKey.toLowerCase()] || 'Unknown';
    const planetSignIndex = VEDIC_SIGNS.indexOf(planetSign);

    if (planetSignIndex >= 0) {
      let relativeHouse = (planetSignIndex - moonSignIndex + 12) % 12 + 1;
      const houseIndex = relativeHouse - 1;
      const symbol = getPlanetSymbol(planet.name, t);

      if (houseIndex >= 0 && houseIndex < 12) {
        chartHouses[houseIndex].planets.push({
          name: planet.name,
          symbol,
          longitude: planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0,
          retrograde: planet.retrograde || false
        });
      }
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1
  };
}

/**
 * Calculate Karak (Significator) Table
 */
function calculateKarakTable(planets: PlanetPosition[]): KarakData {
  const karakData: KarakData = {};

  // Simplified Karak calculation - in real implementation, this would be more complex
  const karakMappings = [
    { karak: 'Atma', sthir: 'Sun', chara: 'Moon' },
    { karak: 'Amatya', sthir: 'Mercury', chara: 'Mars' },
    { karak: 'Bhratru', sthir: 'Mars', chara: 'Saturn' },
    { karak: 'Matru', sthir: 'Moon', chara: 'Jupiter' },
    { karak: 'Putra', sthir: 'Jupiter', chara: 'Mercury' },
    { karak: 'Gnati', sthir: 'Saturn', chara: 'Sun' },
    { karak: 'Dara', sthir: 'Venus', chara: 'Venus' }
  ];

  karakMappings.forEach(mapping => {
    karakData[mapping.karak] = {
      sthir: mapping.sthir,
      chara: mapping.chara
    };
  });

  return karakData;
}

/**
 * Calculate Avastha (Planetary States) Table
 */
function calculateAvasthaTable(planets: PlanetPosition[]): AvasthaData {
  const avasthaData: AvasthaData = {};

  const avasthaStates = [
    { planet: 'Sun', jagrat: 'Swapna', baladi: 'Yuva', deeptadi: 'Deena' },
    { planet: 'Moon', jagrat: 'Jaagrat', baladi: 'Bala', deeptadi: 'Swatha' },
    { planet: 'Mars', jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Deepta' },
    { planet: 'Mercury', jagrat: 'Swapna', baladi: 'Kumar', deeptadi: 'Shant' },
    { planet: 'Jupiter', jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Khal' },
    { planet: 'Venus', jagrat: 'Susupta', baladi: 'Mrat', deeptadi: 'Muditha' },
    { planet: 'Saturn', jagrat: 'Susupta', baladi: 'Vradha', deeptadi: 'Deepta' }
  ];

  avasthaStates.forEach(state => {
    avasthaData[state.planet] = {
      jagrat: state.jagrat,
      baladi: state.baladi,
      deeptadi: state.deeptadi
    };
  });

  return avasthaData;
}

/**
 * Calculate detailed planetary information
 */
function calculatePlanetaryDetails(planets: PlanetPosition[]): PlanetaryDetail[] {
  return planets.map(planet => {
    // Get longitude from planet data structure
    const longitude = planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0;
    const degrees = Math.floor(longitude % 30);
    const minutes = Math.floor((longitude % 1) * 60);
    const seconds = Math.floor(((longitude % 1) * 60 % 1) * 60);

    // Get sign from planet data structure
    const planetSignKey = planet.Sign?.key || planet.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries', 'taurus': 'Taurus', 'gemini': 'Gemini', 'cancer': 'Cancer',
      'leo': 'Leo', 'virgo': 'Virgo', 'libra': 'Libra', 'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius', 'capricorn': 'Capricorn', 'aquarius': 'Aquarius', 'pisces': 'Pisces'
    };

    const capitalizedSign = signMapping[planetSignKey.toLowerCase()] || 'Unknown';

    return {
      planet: planet.name,
      combust: false, // Simplified - real calculation would check Sun proximity
      retrograde: planet.retrograde || false,
      rashi: capitalizedSign,
      longitude: `${degrees}°${minutes}'${seconds}"`,
      nakshatra: planet.nakshatra || 'Unknown',
      pada: Math.floor((longitude % (360/27)) / (360/27/4)) + 1,
      relation: calculatePlanetaryRelation(planet.name, capitalizedSign)
    };
  });
}

/**
 * Calculate planetary relationship with sign lord
 */
function calculatePlanetaryRelation(planetName: string, sign: string): string {
  const signLord = getHouseLord(sign);

  // Simplified relationship calculation
  if (planetName === signLord) return 'Own';

  const friendlyRelations: { [key: string]: string[] } = {
    'Sun': ['Moon', 'Mars', 'Jupiter'],
    'Moon': ['Sun', 'Mercury'],
    'Mars': ['Sun', 'Moon', 'Jupiter'],
    'Mercury': ['Sun', 'Venus'],
    'Jupiter': ['Sun', 'Moon', 'Mars'],
    'Venus': ['Mercury', 'Saturn'],
    'Saturn': ['Mercury', 'Venus']
  };

  const friends = friendlyRelations[planetName] || [];
  if (friends.includes(signLord)) return 'Friendly';

  const enemyRelations: { [key: string]: string[] } = {
    'Sun': ['Venus', 'Saturn'],
    'Moon': ['None'],
    'Mars': ['Mercury'],
    'Mercury': ['Moon'],
    'Jupiter': ['Mercury', 'Venus'],
    'Venus': ['Sun', 'Moon'],
    'Saturn': ['Sun', 'Moon', 'Mars']
  };

  const enemies = enemyRelations[planetName] || [];
  if (enemies.includes(signLord)) return 'Enemy';

  return 'Neutral';
}

/**
 * Calculate Vimshottari Dasha periods
 */
function calculateVimshottariDasha(planets: PlanetPosition[], birthDate: Date): VimshottariDashaData {
  const moonPlanet = planets.find(p => p.name === 'Moon');
  if (!moonPlanet) {
    return {
      currentDasha: 'Unknown',
      balance: '0 years',
      periods: []
    };
  }

  // Get nakshatra and longitude from planet data structure
  const moonNakshatra = moonPlanet.nakshatra || 'Unknown';
  const nakshatraIndex = NAKSHATRAS.indexOf(moonNakshatra);
  const nakshatraLord = NAKSHATRA_LORDS[nakshatraIndex] || 'Unknown';

  // Calculate balance of current dasha at birth
  const moonLongitude = moonPlanet.ChartPosition?.Ecliptic?.DecimalDegrees || moonPlanet.longitude || 0;
  const nakshatraProgress = (moonLongitude % (360/27)) / (360/27);
  const totalDashaPeriod = DASHA_PERIODS[nakshatraLord as keyof typeof DASHA_PERIODS] || 0;
  const completedPeriod = nakshatraProgress * totalDashaPeriod;
  const balanceYears = totalDashaPeriod - completedPeriod;

  // Generate dasha periods
  const periods: DashaPeriod[] = [];
  let currentDate = new Date(birthDate);

  // Start with balance of birth nakshatra lord's dasha
  const balanceEndDate = new Date(currentDate);
  balanceEndDate.setFullYear(balanceEndDate.getFullYear() + Math.floor(balanceYears));
  balanceEndDate.setMonth(balanceEndDate.getMonth() + Math.floor((balanceYears % 1) * 12));

  periods.push({
    planet: nakshatraLord,
    startDate: currentDate.toDateString(),
    endDate: balanceEndDate.toDateString()
  });

  currentDate = new Date(balanceEndDate);

  // Add subsequent dasha periods
  const dashaOrder = ['Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'];
  const startIndex = (dashaOrder.indexOf(nakshatraLord) + 1) % 9;

  for (let i = 0; i < 8; i++) {
    const planetIndex = (startIndex + i) % 9;
    const planet = dashaOrder[planetIndex];
    const period = DASHA_PERIODS[planet as keyof typeof DASHA_PERIODS];

    const endDate = new Date(currentDate);
    endDate.setFullYear(endDate.getFullYear() + period);

    periods.push({
      planet,
      startDate: currentDate.toDateString(),
      endDate: endDate.toDateString()
    });

    currentDate = new Date(endDate);
  }

  return {
    currentDasha: nakshatraLord,
    balance: `${Math.floor(balanceYears)} years ${Math.floor((balanceYears % 1) * 12)} months`,
    periods: periods.slice(0, 9) // Show first 9 periods
  };
}

/**
 * Calculate Ashtakavarga (Simplified version)
 */
function calculateAshtakavarga(planets: PlanetPosition[], houses: HouseInfo[]): AshtakavargaData {
  // This is a simplified Ashtakavarga calculation
  // Real Ashtakavarga involves complex rules for each planet's benefic positions

  const createEmptyTable = (): number[][] => {
    return Array(8).fill(null).map(() => Array(12).fill(0));
  };

  // Initialize tables for each planet
  const sunTable = createEmptyTable();
  const moonTable = createEmptyTable();
  const marsTable = createEmptyTable();
  const mercuryTable = createEmptyTable();
  const jupiterTable = createEmptyTable();
  const venusTable = createEmptyTable();
  const saturnTable = createEmptyTable();
  const ascendantTable = createEmptyTable();

  // Simplified scoring - in real implementation, this would follow traditional Ashtakavarga rules
  planets.forEach((planet, planetIndex) => {
    if (planetIndex < 7) { // Only for traditional 7 planets
      for (let house = 0; house < 12; house++) {
        // Simplified random scoring for demonstration
        // Real implementation would use traditional benefic house rules
        const score = Math.random() > 0.5 ? 1 : 0;

        switch (planet.name) {
          case 'Sun':
            sunTable[planetIndex][house] = score;
            break;
          case 'Moon':
            moonTable[planetIndex][house] = score;
            break;
          case 'Mars':
            marsTable[planetIndex][house] = score;
            break;
          case 'Mercury':
            mercuryTable[planetIndex][house] = score;
            break;
          case 'Jupiter':
            jupiterTable[planetIndex][house] = score;
            break;
          case 'Venus':
            venusTable[planetIndex][house] = score;
            break;
          case 'Saturn':
            saturnTable[planetIndex][house] = score;
            break;
        }
      }
    }
  });

  // Calculate total table (sum of all planetary tables)
  const totalTable = createEmptyTable();
  for (let i = 0; i < 8; i++) {
    for (let j = 0; j < 12; j++) {
      totalTable[i][j] = sunTable[i][j] + moonTable[i][j] + marsTable[i][j] +
                        mercuryTable[i][j] + jupiterTable[i][j] + venusTable[i][j] +
                        saturnTable[i][j] + ascendantTable[i][j];
    }
  }

  // Calculate Sarvashtakavarga (total points for each house)
  const sarvashtakavarga = Array(12).fill(0);
  for (let house = 0; house < 12; house++) {
    for (let planet = 0; planet < 8; planet++) {
      sarvashtakavarga[house] += totalTable[planet][house];
    }
  }

  return {
    sunTable,
    moonTable,
    marsTable,
    mercuryTable,
    jupiterTable,
    venusTable,
    saturnTable,
    ascendantTable,
    totalTable,
    sarvashtakavarga
  };
}
